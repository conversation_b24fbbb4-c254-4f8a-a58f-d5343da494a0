import { Prop } from "@automerge/automerge-repo";
import { ChartType } from "@oneteam/onetheme";

import { getByPath } from "@helpers/configurationFormHelper.ts";

import { Question } from "@src/types/Question.ts";
import {
  CartesianChartConfig,
  ChartConfig,
  ChartQuestionProperties,
  PieChartConfig
} from "@src/types/QuestionProperties.ts";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

// Option 1: Simple approach with union types (Recommended)
export const chartOnChange = ({
  field,
  path,
  docChange
}: {
  field: keyof ChartConfig;
  path: Prop[];
  docChange: (cb: (d: WorkspaceDocument) => void) => void;
}) => {
  return (value: string | string[] | number) => {
    docChange((d: WorkspaceDocument): void => {
      const q = getByPath<Question<ChartQuestionProperties>>(d, path);
      if (!q?.properties) {
        console.error("Question not found", path);
        return;
      }

      const chartConfig = q.properties.chartConfig;

      if (
        value === undefined ||
        value === "" ||
        (Array.isArray(value) && value.length === 0)
      ) {
        // Type-safe deletion
        delete (chartConfig as any)[field];
        return;
      }

      // Runtime check ensures field exists on current config
      if (field in chartConfig) {
        (chartConfig as any)[field] = value;
      }
    });
  };
};

// Option 2: Overloaded functions for specific chart types
export const updateCartesianChart = ({
  field,
  path,
  docChange
}: {
  field: keyof CartesianChartConfig<ChartType.LINE | ChartType.BAR>;
  path: Prop[];
  docChange: (cb: (d: WorkspaceDocument) => void) => void;
}) => {
  return (value: string | string[]) => {
    docChange((d: WorkspaceDocument): void => {
      const q = getByPath<Question<ChartQuestionProperties>>(d, path);
      if (!q?.properties) return;

      const config = q.properties.chartConfig as CartesianChartConfig<
        ChartType.LINE | ChartType.BAR
      >;

      if (!value || (Array.isArray(value) && value.length === 0)) {
        delete (config as any)[field];
      } else {
        (config as any)[field] = value;
      }
    });
  };
};

export const updatePieChart = ({
  field,
  path,
  docChange
}: {
  field: keyof PieChartConfig;
  path: Prop[];
  docChange: (cb: (d: WorkspaceDocument) => void) => void;
}) => {
  return (value: string | string[] | number) => {
    docChange((d: WorkspaceDocument): void => {
      const q = getByPath<Question<ChartQuestionProperties>>(d, path);
      if (!q?.properties) return;

      const config = q.properties.chartConfig as PieChartConfig;

      if (!value || (Array.isArray(value) && value.length === 0)) {
        delete (config as any)[field];
      } else {
        (config as any)[field] = value;
      }
    });
  };
};
